// Quiz Logging Module
(function(global) {
    // Store logs for each session
    let sessionLogs = {
        framework: null,
        quiz: null,
        userResponses: []
    };

    // Logger functions
    function logFramework(frameworkData) {
        sessionLogs.framework = frameworkData;
        console.log('Framework Data:', JSON.stringify(frameworkData, null, 2));
    }

    function logQuiz(quizData) {
        sessionLogs.quiz = quizData;
        console.log('Quiz Data:', JSON.stringify(quizData, null, 2));
    }

    function logUserResponse(response) {
        // Ensure we have the question type in the response
        if (!response.questionType) {
            response.questionType = response.correctAnswer !== null ? "knowledge-check" : "self-assessment";
        }

        // Add skill level for self-assessment if not present
        if (response.questionType === "self-assessment" && !response.skillLevel) {
            // Try to determine skill level from options if possible
            const selectedIndex = response.selectedAnswer === 'SKIPPED' ?
                null :
                quizData[response.questionNumber - 1].options.indexOf(response.selectedAnswer);

            response.skillLevel = selectedIndex !== -1 ? selectedIndex + 1 : null;
        }

        // Add assessment type
        response.assessmentType = 'softSkills';

        // Ensure skillArea is set properly
        if (!response.skillArea && quizData[response.questionNumber - 1]) {
            response.skillArea = quizData[response.questionNumber - 1].skillArea ||
                                quizData[response.questionNumber - 1].course ||
                                'General';
        }

        // Normalize skillArea to match competency names better
        if (response.skillArea) {
            const skillAreaLower = response.skillArea.toLowerCase();
            if (skillAreaLower.includes('communication') && !skillAreaLower.includes('effective')) {
                response.skillArea = 'Effective Communication';
            } else if (skillAreaLower.includes('customer') && !skillAreaLower.includes('excellence')) {
                response.skillArea = 'Customer Service Excellence';
            } else if (skillAreaLower.includes('sales')) {
                response.skillArea = 'Sales Fundamentals';
            } else if (skillAreaLower.includes('telephone') || skillAreaLower.includes('digital')) {
                response.skillArea = 'Telephone and Digital Communication';
            }
        }

        // Enhanced detection of advanced skills for self-assessment
        if (response.questionType === "self-assessment") {
            // Check if the selected answer is the third option (index 2) or contains keywords indicating advanced level
            const isAdvancedOption =
                response.skillLevel === 3 ||
                (response.selectedAnswer && (
                    response.selectedAnswer.toLowerCase().includes('advanced') ||
                    response.selectedAnswer.toLowerCase().includes('expert') ||
                    response.selectedAnswer.toLowerCase().includes('proficient')
                ));

            response.isAdvancedSkill = isAdvancedOption;
            console.log(`Enhanced self-assessment response: SkillArea=${response.skillArea}, SkillLevel=${response.skillLevel}, IsAdvanced=${isAdvancedOption}`);
        }

        sessionLogs.userResponses.push(response);
        console.log('User Response:', JSON.stringify(response, null, 2));
    }

    // Modify existing functions to include logging
    const originalFetchFrameworkData = global.fetchFrameworkData;
    global.fetchFrameworkData = async function(role) {
        const response = await originalFetchFrameworkData(role);
        if (response && frameworkData) {
            logFramework(frameworkData);
        }
        return response;
    };

    // Store reference to the new progressive loadQuizData function
    const originalLoadQuizData = global.loadQuizData;

    // Check if the function exists before wrapping it
    if (typeof originalLoadQuizData === 'function') {
        global.loadQuizData = async function() {
            try {
                await originalLoadQuizData();
                // Note: With progressive loading, quiz data is logged within the loading functions
                // so we don't need to log it again here to avoid duplicates
                console.log('Quiz logger: Progressive loading completed');
            } catch (error) {
                console.error('Quiz logger: Error in loadQuizData:', error);
                throw error;
            }
        };
    } else {
        console.warn('Quiz logger: loadQuizData function not found, logging will be handled by individual loading functions');
    }

    // Modify button event listeners to log responses
    for (let i = 0; i < 4; i++) {
        const originalBtnListener = document.getElementById(`btn${i}`).onclick;
        document.getElementById(`btn${i}`).onclick = function(event) {
            const questionData = quizData[currentQuestion];
            const userResponse = {
                question: questionData.question,
                selectedAnswer: questionData.options[i],
                correctAnswer: questionData.answer,
                isCorrect: questionData.options[i] === questionData.answer,
                questionNumber: currentQuestion + 1,
                section: sectionNames[currentSection - 1],
                timestamp: new Date().toISOString(),
                skillArea: questionData.skillArea || questionData.course || 'General',
                questionType: questionData.type || (questionData.answer ? 'knowledge-check' : 'self-assessment'),
                options: questionData.options
            };
            logUserResponse(userResponse);

            if (originalBtnListener) {
                originalBtnListener.call(this, event);
            }
        };
    }

    // Add skip logging
    const originalSkipListener = document.getElementById('skip-btn').onclick;
    document.getElementById('skip-btn').onclick = function(event) {
        const questionData = quizData[currentQuestion];
        const userResponse = {
            question: questionData.question,
            selectedAnswer: 'SKIPPED',
            correctAnswer: questionData.answer,
            isCorrect: false,
            questionNumber: currentQuestion + 1,
            section: sectionNames[currentSection - 1],
            timestamp: new Date().toISOString(),
            skillArea: questionData.skillArea || questionData.course || 'General',
            questionType: questionData.type || (questionData.answer ? 'knowledge-check' : 'self-assessment'),
            options: questionData.options
        };
        logUserResponse(userResponse);

        if (originalSkipListener) {
            originalSkipListener.call(this, event);
        }
    };

    // Export logging functions
    global.quizLogger = {
        getSessionLogs: () => sessionLogs,
        clearLogs: () => {
            sessionLogs = {
                framework: null,
                quiz: null,
                userResponses: []
            };
        },
        logFinalResults: () => {
            const currentSectionName = sectionNames[currentSection - 1];

            // Filter responses to only include current section
            const currentSectionResponses = sessionLogs.userResponses.filter(response =>
                response.section === currentSectionName
            );

            // Separate responses by type
            const knowledgeCheckResponses = currentSectionResponses.filter(r =>
                r.questionType === "knowledge-check" || !r.questionType
            );

            const selfAssessmentResponses = currentSectionResponses.filter(r =>
                r.questionType === "self-assessment"
            );

            const finalResults = {
                timestamp: new Date().toISOString(),
                userInfo: {
                    email: document.getElementById("email").value.trim(),
                    firstName: document.getElementById("first-name").value.trim(),
                    lastName: document.getElementById("last-name").value.trim(),
                    role: document.getElementById("role").value.trim(),
                    company: userCompany
                },
                quizResults: {
                    totalScore: score,
                    knowledgeCheckQuestions: knowledgeCheckResponses.length,
                    selfAssessmentQuestions: selfAssessmentResponses.length,
                    sectionScores: { [currentSectionName]: sectionScores[currentSectionName] },
                    questionsPerSection: { [currentSectionName]: questionsPerSection[currentSectionName] },
                    currentSection: currentSection,
                    sectionName: currentSectionName,
                    totalQuestions: quizData.length,
                    passThreshold: 0.7 * knowledgeCheckResponses.length,
                    passed: score >= (0.7 * knowledgeCheckResponses.length)
                },
                completeSessionData: {
                    framework: sessionLogs.framework,
                    quiz: sessionLogs.quiz,
                    userResponses: {
                        all: currentSectionResponses,
                        knowledgeCheck: knowledgeCheckResponses,
                        selfAssessment: selfAssessmentResponses
                    },
                    finalTimestamp: new Date().toISOString()
                }
            };

            console.log('Final Quiz Results:', JSON.stringify(finalResults, null, 2));
            return finalResults;
        }
    };

})(typeof window !== 'undefined' ? window : this);