# Quiz Loading Overlay - Complete Implementation Guide

## Overview

The Quiz Loading Overlay is a sophisticated progressive loading system designed for complex assessment initialization. It provides immediate visual feedback, meaningful progress tracking, and smooth transitions between loading phases.

## Architecture

### Core Components
1. **HTML Structure** - Modern overlay container with progress ring
2. **CSS Styling** - Responsive design with animations
3. **JavaScript Logic** - Three-phase loading algorithm
4. **State Management** - Dynamic message system and progress tracking

## HTML Structure

```html
<!-- Modern Progressive Loading Container -->
<div class="modern-loading-overlay" id="quiz-loading-overlay">
  <div class="modern-loading-container">
    <!-- Logo/Brand Section -->
    <div class="loading-brand">
      <img src="logoblack.png" alt="Assessment Logo" class="loading-logo">
    </div>

    <!-- Main Loading Content -->
    <div class="loading-main-content">
      <!-- Animated Progress Ring -->
      <div class="progress-ring-container">
        <svg class="progress-ring" width="140" height="140">
          <defs>
            <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" style="stop-color:#1547bb;stop-opacity:1" />
              <stop offset="100%" style="stop-color:#7ae582;stop-opacity:1" />
            </linearGradient>
          </defs>
          <circle class="progress-ring-background" cx="70" cy="70" r="60"></circle>
          <circle class="progress-ring-fill" cx="70" cy="70" r="60"></circle>
        </svg>
        <div class="progress-percentage" id="progress-percentage">0%</div>

        <!-- Animated Dots in Center -->
        <div class="loading-dots-center">
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
        </div>
      </div>

      <!-- Progressive Loading Messages -->
      <div class="loading-messages">
        <div class="loading-message active" id="quiz-loading-message">
          Personalising your assessment...
        </div>
        <div class="loading-submessage" id="quiz-loading-submessage">
          Preparing your questions
        </div>
      </div>

      <!-- Batch Progress Indicator -->
      <div class="batch-progress-container" id="batch-progress-container">
        <div class="batch-progress-label">Loading Questions</div>
        <div class="batch-progress-track">
          <div class="batch-progress-fill" id="batch-progress-fill"></div>
        </div>
        <div class="batch-progress-text" id="batch-progress-text">Batch 1 of 3</div>
      </div>

      <!-- Loading Steps Indicator -->
      <div class="loading-steps">
        <div class="step active" data-step="1">
          <div class="step-icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
          </div>
          <span>Framework</span>
        </div>
        <div class="step" data-step="2">
          <div class="step-icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2-7H5v2h14V4zM6 19c0 1.1.9 2 2 2h8c0-1.1-.9-2-2-2H6z"/>
            </svg>
          </div>
          <span>Questions</span>
        </div>
        <div class="step" data-step="3">
          <div class="step-icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
          </div>
          <span>Ready</span>
        </div>
      </div>
    </div>

    <!-- Floating Particles Animation -->
    <div class="floating-particles">
      <div class="particle"></div>
      <div class="particle"></div>
      <div class="particle"></div>
      <div class="particle"></div>
      <div class="particle"></div>
    </div>
  </div>
</div>
```

## CSS Styling

### Base Overlay Styles
```css
/* Modern Loading Overlay Styles */
.modern-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("BG.png") no-repeat center center fixed;
    background-size: cover;
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    font-family: 'Montserrat', sans-serif;
}

.modern-loading-overlay.show {
    opacity: 1;
}

.modern-loading-container {
    position: relative;
    text-align: center;
    color: #333333;
    max-width: 400px;
    width: 90%;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 24px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px);
    animation: containerSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}
```

### Progress Ring Styles
```css
/* Progress Ring */
.progress-ring-container {
    position: relative;
    display: inline-block;
    margin-bottom: 2rem;
}

.progress-ring {
    transform: rotate(-90deg);
    filter: drop-shadow(0 0 10px rgba(21, 71, 187, 0.5));
}

.progress-ring-background {
    fill: none;
    stroke: rgba(21, 71, 187, 0.1);
    stroke-width: 6;
}

.progress-ring-fill {
    fill: none;
    stroke: url(#progressGradient);
    stroke-width: 6;
    stroke-linecap: round;
    stroke-dasharray: 377; /* 2 * PI * 60 */
    stroke-dashoffset: 377;
    transition: stroke-dashoffset 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-percentage {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.5rem;
    font-weight: 700;
    color: #1547bb;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
```

### Animated Elements
```css
/* Animated Dots in Center */
.loading-dots-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    gap: 4px;
    margin-top: 20px;
}

.loading-dots-center .dot {
    width: 6px;
    height: 6px;
    background: #1547bb;
    border-radius: 50%;
    animation: dotPulse 1.4s ease-in-out infinite both;
}

.loading-dots-center .dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dots-center .dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dots-center .dot:nth-child(3) { animation-delay: 0s; }

@keyframes dotPulse {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}
```

### Batch Progress Indicator
```css
/* Batch Progress Indicator */
.batch-progress-container {
    margin-bottom: 2rem;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.4s ease;
}

.batch-progress-container.show {
    opacity: 1;
    transform: translateY(0);
}

.batch-progress-track {
    width: 100%;
    height: 6px;
    background: rgba(21, 71, 187, 0.1);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 0.5rem;
    position: relative;
}

.batch-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #1547bb, #7ae582);
    border-radius: 3px;
    width: 0%;
    transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.batch-progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progressShimmer 2s infinite;
}

@keyframes progressShimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}
```

## JavaScript Implementation

### Loading States Configuration
```javascript
// Loading state enumeration
const LoadingState = {
  IDLE: 'idle',
  LOADING_FRAMEWORK: 'loading_framework',
  LOADING_FIRST_BATCH: 'loading_first_batch',
  LOADING_SELF_ASSESSMENT: 'loading_self_assessment',
  LOADING_ADDITIONAL_BATCHES: 'loading_additional_batches',
  COMPLETE: 'complete',
  ERROR: 'error'
};

// Progressive loading messages configuration
const LoadingMessages = {
  [LoadingState.LOADING_FRAMEWORK]: {
    main: "Personalising your assessment...",
    sub: "Preparing your framework"
  },
  [LoadingState.LOADING_FIRST_BATCH]: {
    main: "Updating your questions...",
    sub: "Generating your first set"
  },
  [LoadingState.LOADING_SELF_ASSESSMENT]: {
    main: "Just a moment...",
    sub: "Creating self-assessment questions"
  },
  [LoadingState.LOADING_ADDITIONAL_BATCHES]: {
    main: "Finalising...",
    sub: "Loading additional questions"
  },
  [LoadingState.COMPLETE]: {
    main: "Assessment ready!",
    sub: "Let's begin your journey"
  },
  [LoadingState.ERROR]: {
    main: "Something went wrong",
    sub: "Please try again"
  }
};
```

### Main Loading Function
```javascript
function showQuizLoadingOverlay() {
  const loadingOverlay = document.getElementById('quiz-loading-overlay');
  const progressRing = loadingOverlay.querySelector('.progress-ring-fill');
  const progressPercentage = loadingOverlay.querySelector('.progress-percentage');
  const batchProgressContainer = document.getElementById('batch-progress-container');

  // Reset any previous state
  window.actualLoadingStarted = false;
  if (loadingOverlay.dataset.gradualProgressInterval) {
    clearInterval(Number(loadingOverlay.dataset.gradualProgressInterval));
    delete loadingOverlay.dataset.gradualProgressInterval;
  }

  // Initialize progress ring at 0%
  if (progressRing) {
    progressRing.style.strokeDashoffset = 377; // 0% progress (2 * PI * 60)
  }
  if (progressPercentage) {
    progressPercentage.textContent = '0%';
  }

  // Hide batch progress initially
  if (batchProgressContainer) {
    batchProgressContainer.classList.remove('show');
  }

  // Show loading overlay with modern animation
  loadingOverlay.style.display = 'flex';

  // Use requestAnimationFrame to start as soon as the overlay is in the render tree
  requestAnimationFrame(() => {
    loadingOverlay.classList.add('show');

    // Force a reflow on the progress ring to ensure layout is ready before animating
    const ring = loadingOverlay.querySelector('.progress-ring-fill');
    if (ring) { ring.getBoundingClientRect(); }

    // Adjust for mobile if needed
    if (window.adjustLoadingForMobile) {
      window.adjustLoadingForMobile();
    }

    // Start immediate visual progress feedback within the next paint
    requestAnimationFrame(() => startImmediateProgress(true));
  });
}
```

### Three-Phase Loading Algorithm

#### Phase 1: Immediate Visual Feedback (0-500ms)
```javascript
// Function to start immediate visual progress feedback
function startImmediateProgress(immediateBump = false) {
  const loadingOverlay = document.getElementById('quiz-loading-overlay');
  if (!loadingOverlay) return;

  const progressRing = loadingOverlay.querySelector('.progress-ring-fill');
  const progressPercentage = loadingOverlay.querySelector('.progress-percentage');

  if (!progressRing || !progressPercentage) return;

  const circumference = 377;

  // Optional immediate bump to show activity within ~<100ms of becoming visible
  if (immediateBump) {
    const initial = 5; // 5% right away
    const offset = circumference - (initial / 100) * circumference;
    progressRing.style.strokeDashoffset = offset;
    progressPercentage.textContent = `${initial}%`;
  }

  // Start with immediate progress animation to prevent "stuck" perception
  let currentProgress = 0;
  const targetInitialProgress = 15; // Initial target to show immediate activity
  const animationDuration = 500; // 500ms for initial progress
  const startTime = performance.now();

  function animateInitialProgress() {
    const elapsed = performance.now() - startTime;
    const progress = Math.min(elapsed / animationDuration, 1);

    // Use easeOutCubic for natural deceleration
    const eased = 1 - Math.pow(1 - progress, 3);
    currentProgress = targetInitialProgress * eased;

    const offset = circumference - (currentProgress / 100) * circumference;
    progressRing.style.strokeDashoffset = offset;
    progressPercentage.textContent = `${Math.round(currentProgress)}%`;

    if (progress < 1) {
      requestAnimationFrame(animateInitialProgress);
    } else {
      // Start gradual progress after initial animation
      startGradualProgress(currentProgress);
    }
  }

  requestAnimationFrame(animateInitialProgress);
}
```

#### Phase 2: Gradual Progress (500ms - actual loading)
```javascript
// Function to continue gradual progress after initial burst
function startGradualProgress(startProgress) {
  const loadingOverlay = document.getElementById('quiz-loading-overlay');
  if (!loadingOverlay) return;

  const progressRing = loadingOverlay.querySelector('.progress-ring-fill');
  const progressPercentage = loadingOverlay.querySelector('.progress-percentage');

  if (!progressRing || !progressPercentage) return;

  let currentProgress = startProgress;
  const targetProgress = 25; // Gradual target before actual loading takes over
  const incrementInterval = 100; // Update every 100ms
  const incrementAmount = 0.5; // Small increments to show activity

  const gradualInterval = setInterval(() => {
    if (window.actualLoadingStarted) {
      clearInterval(gradualInterval);
      return;
    }

    if (currentProgress < targetProgress) {
      currentProgress += incrementAmount;
      const circumference = 377;
      const offset = circumference - (currentProgress / 100) * circumference;
      progressRing.style.strokeDashoffset = offset;
      progressPercentage.textContent = `${Math.round(currentProgress)}%`;
    } else {
      clearInterval(gradualInterval);
    }
  }, incrementInterval);

  // Store interval reference for cleanup
  loadingOverlay.dataset.gradualProgressInterval = gradualInterval;
}
```

#### Phase 3: Real Progress Synchronization
```javascript
// Function to take over from visual progress with real loading data
function takeOverFromVisualProgress() {
  window.actualLoadingStarted = true;

  const loadingOverlay = document.getElementById('quiz-loading-overlay');
  if (loadingOverlay && loadingOverlay.dataset.gradualProgressInterval) {
    clearInterval(Number(loadingOverlay.dataset.gradualProgressInterval));
    delete loadingOverlay.dataset.gradualProgressInterval;
  }
}

// Function to update loading progress with real data
function updateLoadingProgress(progress) {
  const loadingOverlay = document.getElementById('quiz-loading-overlay');
  if (!loadingOverlay) return;

  const progressRing = loadingOverlay.querySelector('.progress-ring-fill');
  const progressPercentage = loadingOverlay.querySelector('.progress-percentage');
  const circumference = 377; // 2 * PI * 60 (radius of our ring)

  if (progressRing) {
    const offset = circumference - (progress / 100) * circumference;
    progressRing.style.strokeDashoffset = offset;
  }

  if (progressPercentage) {
    progressPercentage.textContent = `${Math.round(progress)}%`;
  }
}

// Smooth progress ring animation
function animateProgressRing(targetProgress, duration = 1000) {
  const loadingOverlay = document.getElementById('quiz-loading-overlay');
  if (!loadingOverlay) return;

  const progressRing = loadingOverlay.querySelector('.progress-ring-fill');
  const progressPercentage = loadingOverlay.querySelector('.progress-percentage');

  if (!progressRing || !progressPercentage) return;

  const circumference = 377;
  const currentOffset = parseFloat(progressRing.style.strokeDashoffset) || circumference;
  const currentProgress = ((circumference - currentOffset) / circumference) * 100;
  const targetOffset = circumference - (targetProgress / 100) * circumference;

  const startTime = performance.now();

  function animate() {
    const elapsed = performance.now() - startTime;
    const progress = Math.min(elapsed / duration, 1);

    // Use easeInOutCubic for smooth animation
    const eased = progress < 0.5
      ? 4 * progress * progress * progress
      : 1 - Math.pow(-2 * progress + 2, 3) / 2;

    const currentOffset = currentProgress + (targetProgress - currentProgress) * eased;
    const offset = circumference - (currentOffset / 100) * circumference;

    progressRing.style.strokeDashoffset = offset;
    progressPercentage.textContent = `${Math.round(currentOffset)}%`;

    if (progress < 1) {
      requestAnimationFrame(animate);
    }
  }

  requestAnimationFrame(animate);
}
```

### Dynamic Message System
```javascript
// Function to update loading state and provide user feedback
function updateLoadingState(newState, details = {}) {
  const previousState = currentLoadingState;
  currentLoadingState = newState;

  console.log(`Loading state changed: ${previousState} -> ${newState}`, details);

  const loadingMessage = document.getElementById('quiz-loading-message');
  const loadingSubmessage = document.getElementById('quiz-loading-submessage');

  if (!loadingMessage) return;

  // Get message configuration
  let messageConfig = LoadingMessages[newState];

  // Allow custom messages via details
  if (details.customMessage) {
    messageConfig = {
      main: details.customMessage,
      sub: details.customSubmessage || messageConfig?.sub || ''
    };
  }

  if (messageConfig) {
    // Fade out current message
    loadingMessage.style.opacity = '0';
    if (loadingSubmessage) {
      loadingSubmessage.style.opacity = '0';
    }

    // Update messages after fade out
    setTimeout(() => {
      loadingMessage.textContent = messageConfig.main;
      if (loadingSubmessage) {
        loadingSubmessage.textContent = messageConfig.sub;
      }

      // Fade in new messages
      loadingMessage.style.opacity = '1';
      if (loadingSubmessage) {
        setTimeout(() => {
          loadingSubmessage.style.opacity = '1';
        }, 100);
      }
    }, 200);
  }

  // Update step indicators
  updateStepIndicators(newState);

  // Show/hide batch progress based on state
  const batchProgressContainer = document.getElementById('batch-progress-container');
  if (batchProgressContainer) {
    if (newState === LoadingState.LOADING_ADDITIONAL_BATCHES) {
      batchProgressContainer.classList.add('show');
    } else {
      batchProgressContainer.classList.remove('show');
    }
  }
}

// Function to update step indicators
function updateStepIndicators(state) {
  const steps = document.querySelectorAll('.step');

  steps.forEach(step => {
    step.classList.remove('active', 'completed');
  });

  switch (state) {
    case LoadingState.LOADING_FRAMEWORK:
      steps[0]?.classList.add('active');
      break;
    case LoadingState.LOADING_FIRST_BATCH:
    case LoadingState.LOADING_SELF_ASSESSMENT:
    case LoadingState.LOADING_ADDITIONAL_BATCHES:
      steps[0]?.classList.add('completed');
      steps[1]?.classList.add('active');
      break;
    case LoadingState.COMPLETE:
      steps[0]?.classList.add('completed');
      steps[1]?.classList.add('completed');
      steps[2]?.classList.add('active');
      break;
  }
}
```

### Batch Progress Tracking
```javascript
// Function to update batch loading progress
function updateBatchProgress(batchNumber, status, totalBatches = 3) {
  const batchProgressFill = document.getElementById('batch-progress-fill');
  const batchProgressText = document.getElementById('batch-progress-text');

  if (!batchProgressFill || !batchProgressText) return;

  // Update progress based on completed batches
  const completedBatches = status === 'completed' ? batchNumber : batchNumber - 1;
  const progressPercentage = (completedBatches / totalBatches) * 100;

  batchProgressFill.style.width = `${progressPercentage}%`;
  batchProgressText.textContent = `Batch ${batchNumber} of ${totalBatches}`;

  // Update loading state if we're in the additional batches loading state
  if (currentLoadingState === LoadingState.LOADING_ADDITIONAL_BATCHES) {
    updateLoadingState(LoadingState.LOADING_ADDITIONAL_BATCHES);
  }

  console.log(`Batch ${batchNumber} ${status}. Progress:`, batchLoadingProgress);
}
```

### Error Handling
```javascript
// Function to handle loading errors gracefully
function handleLoadingError(error, context = '') {
  console.error(`Loading error in ${context}:`, error);
  loadingErrors.push({ error: error.message, context, timestamp: Date.now() });

  // Show user-friendly error message
  const loadingMessage = document.getElementById('quiz-loading-message');
  if (loadingMessage) {
    loadingMessage.textContent = `Having trouble loading questions. Retrying...`;
  }

  // Update progress to show we're handling the error
  updateLoadingProgress(Math.max(currentProgress - 10, 10));

  // Update to error state if multiple failures
  if (loadingErrors.length > 2) {
    updateLoadingState(LoadingState.ERROR);
  }
}
```

### Hide Loading Overlay
```javascript
function hideQuizLoadingOverlay() {
  const loadingOverlay = document.getElementById('quiz-loading-overlay');
  if (!loadingOverlay) return;

  // Clear any running intervals
  if (loadingOverlay.dataset.gradualProgressInterval) {
    clearInterval(Number(loadingOverlay.dataset.gradualProgressInterval));
    delete loadingOverlay.dataset.gradualProgressInterval;
  }

  // Show completion state briefly
  updateLoadingState(LoadingState.COMPLETE);
  updateLoadingProgress(100);

  // Hide overlay with animation
  setTimeout(() => {
    loadingOverlay.classList.remove('show');
    setTimeout(() => {
      loadingOverlay.style.display = 'none';

      // Reset for next use
      const progressRing = loadingOverlay.querySelector('.progress-ring-fill');
      const progressPercentage = loadingOverlay.querySelector('.progress-percentage');
      const batchProgressContainer = document.getElementById('batch-progress-container');

      if (progressRing) progressRing.style.strokeDashoffset = 377;
      if (progressPercentage) progressPercentage.textContent = '0%';
      if (batchProgressContainer) batchProgressContainer.classList.remove('show');

      // Reset state
      window.actualLoadingStarted = false;
      currentLoadingState = LoadingState.IDLE;
    }, 400);
  }, 1000);
}
```
