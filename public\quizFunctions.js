(function(global) {
    // Client-side request tracking to prevent duplicate requests
    const pendingRequests = new Set();

    // Global framework cache to share between components
    if (!window.frameworkCache) {
      window.frameworkCache = {};
    }

    // Global question cache to prevent duplicate API requests
    if (!window.questionCache) {
      window.questionCache = {};
    }

    // Helper function to check if a request is pending
    function isRequestPending(endpoint, params) {
      const requestKey = `${endpoint}_${JSON.stringify(params)}`;
      return pendingRequests.has(requestKey);
    }

    // Helper function to mark a request as pending
    function markRequestPending(endpoint, params) {
      const requestKey = `${endpoint}_${JSON.stringify(params)}`;
      pendingRequests.add(requestKey);
      console.log(`Request marked as pending: ${requestKey}`);
      return requestKey;
    }

    // Helper function to mark a request as completed
    function markRequestCompleted(requestKey) {
      pendingRequests.delete(requestKey);
      console.log(`Request marked as completed: ${requestKey}`);
    }

    // Helper function to cache framework data
    function cacheFramework(role, framework) {
      window.frameworkCache[role] = framework;
      console.log(`Framework cached for role: ${role}`);
    }

    // Helper function to get cached framework data
    function getCachedFramework(role) {
      return window.frameworkCache[role];
    }

    // Helper function to cache questions
    function cacheQuestions(role, section, type, questions) {
      const cacheKey = `${role}_${section}_${type}`;
      window.questionCache[cacheKey] = questions;
      console.log(`Cached ${type} questions for ${role}/${section}`);
    }

    // Helper function to get cached questions
    function getCachedQuestions(role, section) {
      const cacheKey = `${role}_${section}_regular`;
      return window.questionCache[cacheKey];
    }

    // Helper function to get cached self-assessment questions
    function getCachedSelfAssessmentQuestions(role, section) {
      const cacheKey = `${role}_${section}_self-assessment`;
      return window.questionCache[cacheKey];
    }

    const startQuiz = () => {
        // Reset all scores and global state variables
        currentQuestion = 0;
        score = 0;
        sectionScores = {
          "Essentials": 0,
          "Intermediate": 0,
          "Advanced": 0,
          "Champions": 0
        };
        questionsPerSection = {
          "Essentials": 0,
          "Intermediate": 0,
          "Advanced": 0,
          "Champions": 0
        };
        isFinalSuccessContainerDisplayed = false;
        isFailureContainerDisplayed = false;

        document.getElementById("start-page").style.display = "none";
        document.getElementById("consent-popup").style.display = "block";
      };


const endQuiz = async () => {
  const finalResults = window.quizLogger ? window.quizLogger.logFinalResults() : null;
  document.getElementById("quiz-container").style.display = "none";

  // Calculate score based only on knowledge-check questions
  const knowledgeCheckQuestions = quizData.filter(q => q.type === "knowledge-check");
  const totalKnowledgeQuestions = knowledgeCheckQuestions.length;
  const passThreshold = 0.7 * totalKnowledgeQuestions;

  if (score >= passThreshold) {
    if (currentSection === 4) {
        const email = document.getElementById("email").value.trim();
        if (email) {
            try {
                await db.collection('assessmentStatus').doc(email).update({
                    status: 'completed',
                    timestamp: firebase.firestore.FieldValue.serverTimestamp()
                });
            } catch (error) {
                console.error('Error updating assessment status to Firestore:', error);
                showNotification('Error updating assessment status. Please try again.', 'error');
            }
        }
        isFinalSuccessContainerDisplayed = true;
        document.getElementById("final-success-container").style.display = "block";
    } else {
        document.getElementById("success-container").style.display = "block";
        document.getElementById("current-section").innerText = currentSection;
        const successHeading = document.getElementById("success-heading");
        successHeading.innerText = `You passed Section ${currentSection}: ${sectionNames[currentSection - 1]}`;
    }
  } else {
    showLoadingOverlay();
    document.getElementById("failure-container").style.display = "block";
    isFailureContainerDisplayed = true;

    const email = document.getElementById("email").value.trim();

    if (email) {
        try {
            const firstName = document.getElementById("first-name").value.trim();
            const lastName = document.getElementById("last-name").value.trim();
            const phone = document.getElementById("phone").value.trim();
            const role = document.getElementById("role").value.trim();

            // Create the assessment summary object
            const assessmentSummary = {
                timestamp: firebase.firestore.FieldValue.serverTimestamp(),
                totalScore: score,
                totalKnowledgeQuestions: totalKnowledgeQuestions,
                sectionScores: { ...sectionScores },
                questionsPerSection: { ...questionsPerSection },
                currentSection: currentSection,
                status: 'completed'
            };

            const resultData = {
                employeeEmail: email,
                section: sectionNames[currentSection - 1],
                score,
                role,
                totalQuestions: totalKnowledgeQuestions, // Only count knowledge questions for scoring
                isNewUser: false,
                timestamp: firebase.firestore.FieldValue.serverTimestamp(),
                firstName,
                lastName,
                userCompany: userCompany,
                userPhone: phone,
                assessmentType: 'softSkills' // Add assessment type
            };

            const companyRef = db.collection('companies').doc(userCompany);
            const userRef = companyRef.collection('users').doc(email);
            const assessmentResultsRef = userRef.collection('softSkillsAssessmentResults');

            // Save the assessment summary to a new collection
            await userRef.collection('softSkillsSummaries').add(assessmentSummary);
            console.log('Soft skills assessment summary saved to Firestore:', assessmentSummary);

            // Add the new assessment result
            await assessmentResultsRef.add(resultData);
            console.log('Assessment result saved to Firestore:', resultData);

            // Update the assessment status under the user document with soft skills specific fields
            await userRef.update({
                status: 'completed',
                lastSoftSkillsAssessmentId: assessmentResultsRef.id,
                lastSoftSkillsAssessmentDate: firebase.firestore.FieldValue.serverTimestamp()
            });

            hideLoadingOverlay();
            showFeedbackPrompt();
            initializePathwayButton();
            pollForRecommendations(email, userCompany);

            showNotification('Your results are being processed. You will be notified when complete.', 'success');

            sendAssessmentResult(email).then(() => {
                console.log('Assessment result sent for recommendations');
                showNotification('Your results have been processed successfully!', 'success');
            }).catch(error => {
                console.error('Error sending assessment result for recommendations:', error);
                showNotification('An error occurred while processing your results. Please contact support.', 'error');
            });

        } catch (error) {
            console.error('Error saving assessment data to Firestore:', error);
            hideLoadingOverlay();
            showNotification('An error occurred while submitting assessment data. Please try again or contact support.', 'error');
        }
    } else {
        console.log('Email is not provided, assessment data will not be saved.');
        hideLoadingOverlay();
    }
  }
};

      const logFinalResults = () => {
        const currentSectionName = sectionNames[currentSection - 1];

        // Filter responses to only include current section
        const currentSectionResponses = sessionLogs.userResponses.filter(response =>
            response.section === currentSectionName
        );

        const finalResults = {
            timestamp: new Date().toISOString(),
            userInfo: {
                email: document.getElementById("email").value.trim(),
                firstName: document.getElementById("first-name").value.trim(),
                lastName: document.getElementById("last-name").value.trim(),
                role: document.getElementById("role").value.trim(),
                company: userCompany
            },
            quizResults: {
                totalScore: score,
                sectionScores: { [currentSectionName]: sectionScores[currentSectionName] },
                questionsPerSection: { [currentSectionName]: questionsPerSection[currentSectionName] },
                currentSection: currentSection,
                sectionName: currentSectionName,
                totalQuestions: quizData.length,
                passThreshold: 0.7 * quizData.length,
                passed: score >= (0.7 * quizData.length)
            },
            completeSessionData: {
                framework: sessionLogs.framework,
                quiz: sessionLogs.quiz,
                userResponses: currentSectionResponses,
                finalTimestamp: new Date().toISOString()
            }
        };

        console.log('Final Quiz Results:', JSON.stringify(finalResults, null, 2));
        return finalResults;
    };

    // Modern Loading System Configuration
const LoadingState = {
  IDLE: 'idle',
  LOADING_FRAMEWORK: 'loading_framework',
  LOADING_FIRST_BATCH: 'loading_first_batch',
  LOADING_SELF_ASSESSMENT: 'loading_self_assessment',
  LOADING_ADDITIONAL_BATCHES: 'loading_additional_batches',
  COMPLETE: 'complete',
  ERROR: 'error'
};

const LoadingMessages = {
  [LoadingState.LOADING_FRAMEWORK]: {
    main: "Personalising your assessment...",
    sub: "Preparing your framework"
  },
  [LoadingState.LOADING_FIRST_BATCH]: {
    main: "Updating your questions...",
    sub: "Generating your first set"
  },
  [LoadingState.LOADING_SELF_ASSESSMENT]: {
    main: "Just a moment...",
    sub: "Creating self-assessment questions"
  },
  [LoadingState.LOADING_ADDITIONAL_BATCHES]: {
    main: "Finalising...",
    sub: "Loading additional questions"
  },
  [LoadingState.COMPLETE]: {
    main: "Assessment ready!",
    sub: "Let's begin your journey"
  },
  [LoadingState.ERROR]: {
    main: "Something went wrong",
    sub: "Please try again"
  }
};

// Progressive Loading Configuration
const PROGRESSIVE_LOADING_CONFIG = {
  INITIAL_BATCH_SIZE: 5,  // First batch of questions to load immediately
  TOTAL_QUESTIONS: 20,    // Total questions expected (10 regular + 10 self-assessment)
  BACKGROUND_BATCH_SIZE: 15 // Remaining questions to load in background
};

// Global variables for progressive loading
let totalQuestionCount = PROGRESSIVE_LOADING_CONFIG.TOTAL_QUESTIONS;
let initialQuestionsLoaded = false;
let backgroundLoadingInProgress = false;
let allQuestionsLoaded = false;

// Progressive Loading Implementation - Replace the loadQuizData function
const loadQuizData = async () => {
  showQuizLoadingOverlay();

  // Update loading state to framework loading
  updateLoadingState(LoadingState.LOADING_FRAMEWORK);

  let currentProgress = 0;
  updateLoadingProgress(currentProgress);

  // Take over from visual progress
  setTimeout(() => {
    takeOverFromVisualProgress();
  }, 1000);

  try {
    // Start progressive loading - load initial batch first
    await loadInitialQuestionBatch();

    // Start background loading of remaining questions
    startBackgroundQuestionLoading();

  } catch (error) {
    console.error('Error in progressive loading:', error);
    handleLoadingError(error, 'loadQuizData');

    // Fallback - hide loading and show error
    hideQuizLoadingOverlay();
    showNotification('Failed to load quiz questions. Please try again.', 'error');
    throw error;
  }




// Progressive Loading Functions



// Progressive Loading Functions

// Function to load initial batch of questions
const loadInitialQuestionBatch = async () => {
  updateLoadingState(LoadingState.LOADING_FIRST_BATCH);
  updateLoadingProgress(30);

  const role = document.getElementById("role").value.trim();
  const currentSectionName = sectionNames[currentSection - 1];

  // Get framework
  let framework = getCachedFramework(role);
  if (!framework) {
    const frameworkDoc = await db.collection('frameworks').doc(role).get();
    if (!frameworkDoc.exists) {
      throw new Error('Framework not found for role');
    }
    framework = frameworkDoc.data();
    cacheFramework(role, framework);
  }

  // Request initial batch of questions (first 5 questions)
  const email = document.getElementById("email")?.value.trim() || null;

  const initialBatchResponse = await fetch('/api/generate-initial-batch', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      role,
      section: currentSectionName,
      framework,
      email,
      batchSize: PROGRESSIVE_LOADING_CONFIG.INITIAL_BATCH_SIZE
    }),
  });

  if (!initialBatchResponse.ok) {
    // Fallback: if initial batch endpoint doesn't exist, use regular endpoint
    console.log('Initial batch endpoint not available, falling back to regular quiz generation');
    const fallbackResponse = await fetch('/api/generate-quiz', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        role,
        section: currentSectionName,
        framework,
        email
      }),
    });

    if (!fallbackResponse.ok) {
      throw new Error(`Failed to load questions: ${fallbackResponse.statusText}`);
    }

    const allQuestions = await fallbackResponse.json();
    // Use first 5 questions as initial batch
    const initialQuestions = allQuestions.slice(0, PROGRESSIVE_LOADING_CONFIG.INITIAL_BATCH_SIZE);

    // Store remaining questions for background loading
    window.remainingQuestions = allQuestions.slice(PROGRESSIVE_LOADING_CONFIG.INITIAL_BATCH_SIZE);

    return initializeQuizWithQuestions(initialQuestions, role, currentSectionName);
  }

  const initialQuestions = await initialBatchResponse.json();
  return initializeQuizWithQuestions(initialQuestions, role, currentSectionName);
};

// Helper function to initialize quiz with questions
const initializeQuizWithQuestions = (initialQuestions, role, currentSectionName) => {
  // Initialize quiz with initial questions
  quizData = initialQuestions;
  questionsPerSection[currentSectionName] = totalQuestionCount; // Use predetermined total
  sectionScores[currentSectionName] = 0;

  // Update UI elements
  document.getElementById("current-section").innerText = currentSection;
  document.getElementById("section-name").innerText = currentSectionName;

  // Log initial questions
  if (window.quizLogger && typeof window.quizLogger.logQuiz === 'function') {
    window.quizLogger.logQuiz(initialQuestions);
  }

  initialQuestionsLoaded = true;
  updateLoadingProgress(50);

  console.log(`Initial batch loaded: ${initialQuestions.length} questions`);

  // Hide loading overlay and start quiz
  setTimeout(() => {
    hideQuizLoadingOverlay();
    loadQuestion();
    updateProgressBar();

    // Set up button handlers after quiz is loaded
    if (typeof setupButtonHandlers === 'function') {
      setupButtonHandlers();
    }
    if (typeof setupSkipButtonHandler === 'function') {
      setupSkipButtonHandler();
    }

    console.log('Quiz started with initial batch');
  }, 500);
};

// Function to start background loading of remaining questions
const startBackgroundQuestionLoading = async () => {
  if (backgroundLoadingInProgress) return;

  backgroundLoadingInProgress = true;

  // Wait a bit to let user start answering
  setTimeout(async () => {
    try {
      await loadRemainingQuestions();
    } catch (error) {
      console.error('Background loading failed:', error);
      // Continue with initial questions only
    }
  }, 2000); // Start background loading after 2 seconds
};

// Function to load remaining questions in background
const loadRemainingQuestions = async () => {
  updateLoadingState(LoadingState.LOADING_ADDITIONAL_BATCHES);

  const role = document.getElementById("role").value.trim();
  const currentSectionName = sectionNames[currentSection - 1];
  const framework = getCachedFramework(role);
  const email = document.getElementById("email")?.value.trim() || null;

  // Show batch progress
  updateBatchProgress(1, 'loading', 3);

  try {
    // Check if we have remaining questions from fallback
    if (window.remainingQuestions && window.remainingQuestions.length > 0) {
      console.log('Using remaining questions from fallback');

      updateBatchProgress(2, 'loading', 3);

      // Load self-assessment questions
      const selfAssessmentResponse = await fetch('/api/generate-self-assessment', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          role,
          section: currentSectionName,
          framework,
          email
        }),
      });

      updateBatchProgress(3, 'completed', 3);

      if (selfAssessmentResponse.ok) {
        const selfAssessmentQuestions = await selfAssessmentResponse.json();

        // Add remaining questions to quiz data
        const allRemainingQuestions = [...window.remainingQuestions, ...selfAssessmentQuestions];
        quizData = [...quizData, ...shuffleArray(allRemainingQuestions)];

        // Clear the temporary storage
        delete window.remainingQuestions;

        // Log additional questions
        if (window.quizLogger && typeof window.quizLogger.logQuiz === 'function') {
          window.quizLogger.logQuiz(allRemainingQuestions);
        }

        allQuestionsLoaded = true;
        console.log(`Background loading complete. Total questions: ${quizData.length}`);

        // Update progress bar to reflect new total if user hasn't finished yet
        if (currentQuestion < quizData.length) {
          updateProgressBar();
        }
      }
    } else {
      // Load remaining regular questions
      const regularQuestionsResponse = await fetch('/api/generate-quiz', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          role,
          section: currentSectionName,
          framework,
          email,
          excludeInitialBatch: true,
          remainingCount: 10 - PROGRESSIVE_LOADING_CONFIG.INITIAL_BATCH_SIZE
        }),
      });

      updateBatchProgress(2, 'loading', 3);

      // Load self-assessment questions
      const selfAssessmentResponse = await fetch('/api/generate-self-assessment', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          role,
          section: currentSectionName,
          framework,
          email
        }),
      });

      updateBatchProgress(3, 'completed', 3);

      if (regularQuestionsResponse.ok && selfAssessmentResponse.ok) {
        const remainingRegularQuestions = await regularQuestionsResponse.json();
        const selfAssessmentQuestions = await selfAssessmentResponse.json();

        // Add remaining questions to quiz data
        const allRemainingQuestions = [...remainingRegularQuestions, ...selfAssessmentQuestions];
        quizData = [...quizData, ...shuffleArray(allRemainingQuestions)];

        // Log additional questions
        if (window.quizLogger && typeof window.quizLogger.logQuiz === 'function') {
          window.quizLogger.logQuiz(allRemainingQuestions);
        }

        allQuestionsLoaded = true;
        console.log(`Background loading complete. Total questions: ${quizData.length}`);

        // Update progress bar to reflect new total if user hasn't finished yet
        if (currentQuestion < quizData.length) {
          updateProgressBar();
        }
      }
    }
  } catch (error) {
    console.error('Background loading failed:', error);
    // Continue with initial questions only
  }

  backgroundLoadingInProgress = false;
};

// Fallback function for traditional loading
const loadAllQuestionsTraditional = async () => {
  // This is the original loadQuizData logic as fallback
  console.log('Falling back to traditional loading method');

  const role = document.getElementById("role").value.trim();
  const currentSectionName = sectionNames[currentSection - 1];

  // ... (implement traditional loading as fallback)
  // For now, just throw error to be handled by caller
  throw new Error('Traditional loading fallback not implemented yet');
};

// Helper function to shuffle an array (Fisher-Yates algorithm)
function shuffleArray(array) {
  const newArray = [...array];
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
  }
  return newArray;
}


      function showQuestionLoadingState() {
        document.getElementById("question").innerText = "Loading questions...";
        document.getElementById("progress-bar-fill").style.width = "0%";
        document.getElementById("progress-bar-text").innerText = "0%";

        const optionButtons = document.querySelectorAll('.option-btn');
        optionButtons.forEach(btn => {
          btn.disabled = true;
          btn.innerText = 'Loading...';
        });

        document.getElementById("skip-btn").disabled = true;
        document.getElementById("message").innerText = "";
      }

      const loadQuestion = () => {
        const questionObj = quizData[currentQuestion];
        document.getElementById("question").innerText = questionObj.question;

        // Get the options container
        const optionsContainer = document.getElementById("options-container");

        // Apply question type specific styling
        if (questionObj.type === "self-assessment") {
          optionsContainer.classList.add("self-assessment");
          // Show a subtle indicator for self-assessment questions
          document.getElementById("message").innerHTML = '<span class="self-assessment-indicator">Self-Assessment Question</span>';
        } else {
          optionsContainer.classList.remove("self-assessment");
          document.getElementById("message").innerText = "";
        }

        // Handle different number of options (3 for self-assessment, 4 for knowledge check)
        const optionCount = questionObj.type === "self-assessment" ? 3 : 4;

        // Display options
        for (let i = 0; i < 4; i++) {
          const btn = document.getElementById(`btn${i}`);

          if (i < optionCount) {
            btn.innerText = questionObj.options[i];
            btn.className = "option-btn";
            btn.dataset.type = questionObj.type;
            btn.dataset.level = i + 1; // Store level for self-assessment (1=basic, 2=intermediate, 3=advanced)
            btn.disabled = false;
            btn.style.opacity = 1;
            btn.style.cursor = "pointer";
            btn.style.display = "block";
          } else {
            // Hide extra buttons if not needed (for self-assessment with 3 options)
            btn.style.display = "none";
          }
        }

        document.getElementById("skip-btn").disabled = false;
        document.getElementById("skip-btn").style.opacity = 1;
        document.getElementById("skip-btn").style.cursor = "pointer";
      };

        const restartQuiz = () => {
        currentQuestion = 0;
        score = 0;
        document.getElementById("score").innerText = "0";
        document.getElementById("failure-container").style.display = "none";
        document.getElementById("start-page").style.display = "block";
        document.getElementById("progress-bar-fill").style.width = "0%";
        document.getElementById("progress-bar-text").innerText = "0%";
        loadQuizData();
      };


    // Toast notification function from the framework
    function showToast(message, duration = 5000) {
        const toast = document.createElement('div');
        toast.classList.add('toast');
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                toast.remove();
            }, 300);
        }, duration);
    }

    function updateProgressBar() {
      // Use predetermined total question count for accurate progress calculation
      const progress = (currentQuestion / totalQuestionCount) * 100;
      console.log('Progress:', progress, `(${currentQuestion}/${totalQuestionCount})`);

      // Update progress bar fill
      const progressBarFill = document.getElementById("progress-bar-fill");
      progressBarFill.style.width = `${progress}%`;

      // Check if progress text container exists
      let progressBarText = document.getElementById("progress-bar-text");

      if (!progressBarText) {
        // Create a new container for the progress text if it doesn't exist
        progressBarText = document.createElement("div");
        progressBarText.id = "progress-bar-text";
        progressBarText.className = "text-center text-gray-700 mb-2"; // Added proper styling classes

        // Get the progress bar container
        const progressBar = document.getElementById("progress-bar");
        // Insert the text BEFORE the progress bar
        progressBar.parentNode.insertBefore(progressBarText, progressBar);
      }

      // Update the progress text with current/total format
      progressBarText.textContent = `Progress: ${Math.round(progress)}% (${currentQuestion}/${totalQuestionCount})`;

      // Show loading indicator if background loading is in progress and user is near the end of initial batch
      if (backgroundLoadingInProgress && currentQuestion >= PROGRESSIVE_LOADING_CONFIG.INITIAL_BATCH_SIZE - 1) {
        const loadingIndicator = document.getElementById("background-loading-indicator");
        if (!loadingIndicator) {
          const indicator = document.createElement("div");
          indicator.id = "background-loading-indicator";
          indicator.className = "text-center text-blue-600 text-sm mt-2";
          indicator.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading additional questions...';
          progressBarText.parentNode.insertBefore(indicator, progressBarText.nextSibling);
        }
      } else if (allQuestionsLoaded) {
        // Remove loading indicator when all questions are loaded
        const loadingIndicator = document.getElementById("background-loading-indicator");
        if (loadingIndicator) {
          loadingIndicator.remove();
        }
      }
    }

    global.startQuiz = startQuiz;
    global.endQuiz = endQuiz;
    global.loadQuizData = loadQuizData;
    global.loadQuestion = loadQuestion;
    global.updateProgressBar = updateProgressBar;
    global.restartQuiz = restartQuiz;
    global.logFinalResults = logFinalResults;
    window.showToast = showToast;
  })(typeof window !== 'undefined' ? window : global);
